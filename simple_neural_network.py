"""
极简神经网络实现 - 使用纯Python标准库
作者：AI助手
功能：实现一个简单的前馈神经网络，用于二分类问题
"""

import math
import random


class SimpleNeuralNetwork:
    """
    极简神经网络类
    
    网络结构：
    - 输入层：2个神经元
    - 隐藏层：3个神经元
    - 输出层：1个神经元
    """
    
    def __init__(self, learning_rate=0.1):
        """
        初始化神经网络
        
        参数:
            learning_rate (float): 学习率，控制权重更新的步长
        """
        self.learning_rate = learning_rate
        
        # 初始化权重和偏置
        # 输入层到隐藏层的权重 (2x3矩阵)
        self.weights_input_hidden = [
            [random.uniform(-1, 1) for _ in range(3)] for _ in range(2)
        ]
        
        # 隐藏层到输出层的权重 (3x1矩阵)
        self.weights_hidden_output = [
            [random.uniform(-1, 1)] for _ in range(3)
        ]
        
        # 隐藏层偏置 (3个神经元)
        self.bias_hidden = [random.uniform(-1, 1) for _ in range(3)]
        
        # 输出层偏置 (1个神经元)
        self.bias_output = [random.uniform(-1, 1)]
    
    def sigmoid(self, x):
        """
        Sigmoid激活函数
        
        参数:
            x (float): 输入值
            
        返回:
            float: 激活后的值，范围在(0,1)之间
        """
        # 防止数值溢出
        if x > 500:
            return 1.0
        elif x < -500:
            return 0.0
        return 1 / (1 + math.exp(-x))
    
    def sigmoid_derivative(self, x):
        """
        Sigmoid函数的导数
        
        参数:
            x (float): Sigmoid函数的输出值
            
        返回:
            float: 导数值
        """
        return x * (1 - x)
    
    def forward_propagation(self, inputs):
        """
        前向传播
        
        参数:
            inputs (list): 输入数据，长度为2的列表
            
        返回:
            tuple: (隐藏层输出, 最终输出)
        """
        # 计算隐藏层的输入
        hidden_inputs = []
        for j in range(3):  # 3个隐藏层神经元
            weighted_sum = 0
            for i in range(2):  # 2个输入
                weighted_sum += inputs[i] * self.weights_input_hidden[i][j]
            weighted_sum += self.bias_hidden[j]
            hidden_inputs.append(weighted_sum)
        
        # 计算隐藏层的输出（应用激活函数）
        hidden_outputs = [self.sigmoid(x) for x in hidden_inputs]
        
        # 计算输出层的输入
        output_input = 0
        for j in range(3):  # 3个隐藏层神经元
            output_input += hidden_outputs[j] * self.weights_hidden_output[j][0]
        output_input += self.bias_output[0]
        
        # 计算最终输出（应用激活函数）
        final_output = self.sigmoid(output_input)
        
        return hidden_outputs, final_output
    
    def backward_propagation(self, inputs, target, hidden_outputs, final_output):
        """
        反向传播算法
        
        参数:
            inputs (list): 输入数据
            target (float): 目标输出
            hidden_outputs (list): 隐藏层输出
            final_output (float): 网络的最终输出
        """
        # 计算输出层的误差
        output_error = target - final_output
        output_delta = output_error * self.sigmoid_derivative(final_output)
        
        # 计算隐藏层的误差
        hidden_errors = []
        hidden_deltas = []
        for j in range(3):  # 3个隐藏层神经元
            error = output_delta * self.weights_hidden_output[j][0]
            delta = error * self.sigmoid_derivative(hidden_outputs[j])
            hidden_errors.append(error)
            hidden_deltas.append(delta)
        
        # 更新隐藏层到输出层的权重
        for j in range(3):
            self.weights_hidden_output[j][0] += self.learning_rate * output_delta * hidden_outputs[j]
        
        # 更新输出层偏置
        self.bias_output[0] += self.learning_rate * output_delta
        
        # 更新输入层到隐藏层的权重
        for i in range(2):  # 2个输入
            for j in range(3):  # 3个隐藏层神经元
                self.weights_input_hidden[i][j] += self.learning_rate * hidden_deltas[j] * inputs[i]
        
        # 更新隐藏层偏置
        for j in range(3):
            self.bias_hidden[j] += self.learning_rate * hidden_deltas[j]
    
    def train(self, training_data, epochs=1000):
        """
        训练神经网络
        
        参数:
            training_data (list): 训练数据，格式为[(inputs, target), ...]
            epochs (int): 训练轮数
        """
        print(f"开始训练，共{epochs}轮...")
        
        for epoch in range(epochs):
            total_error = 0
            
            # 遍历所有训练样本
            for inputs, target in training_data:
                # 前向传播
                hidden_outputs, final_output = self.forward_propagation(inputs)
                
                # 计算误差
                error = (target - final_output) ** 2
                total_error += error
                
                # 反向传播
                self.backward_propagation(inputs, target, hidden_outputs, final_output)
            
            # 每100轮打印一次误差
            if epoch % 100 == 0:
                avg_error = total_error / len(training_data)
                print(f"第{epoch}轮，平均误差: {avg_error:.6f}")
        
        print("训练完成！")
    
    def predict(self, inputs):
        """
        预测函数
        
        参数:
            inputs (list): 输入数据
            
        返回:
            float: 预测结果
        """
        _, output = self.forward_propagation(inputs)
        return output


def main():
    """
    主函数 - 演示神经网络的使用
    """
    print("=" * 50)
    print("极简神经网络演示")
    print("=" * 50)
    
    # 创建神经网络实例
    nn = SimpleNeuralNetwork(learning_rate=0.5)
    
    # 准备训练数据 - XOR问题
    # XOR是一个经典的非线性分类问题
    training_data = [
        ([0, 0], 0),  # 0 XOR 0 = 0
        ([0, 1], 1),  # 0 XOR 1 = 1
        ([1, 0], 1),  # 1 XOR 0 = 1
        ([1, 1], 0),  # 1 XOR 1 = 0
    ]
    
    print("训练数据（XOR问题）:")
    for inputs, target in training_data:
        print(f"输入: {inputs}, 目标输出: {target}")
    print()
    
    # 训练前的预测
    print("训练前的预测结果:")
    for inputs, target in training_data:
        prediction = nn.predict(inputs)
        print(f"输入: {inputs}, 预测: {prediction:.4f}, 目标: {target}")
    print()
    
    # 训练神经网络
    nn.train(training_data, epochs=5000)
    print()
    
    # 训练后的预测
    print("训练后的预测结果:")
    for inputs, target in training_data:
        prediction = nn.predict(inputs)
        print(f"输入: {inputs}, 预测: {prediction:.4f}, 目标: {target}")
    print()
    
    # 测试准确性
    print("准确性测试:")
    correct = 0
    for inputs, target in training_data:
        prediction = nn.predict(inputs)
        predicted_class = 1 if prediction > 0.5 else 0
        if predicted_class == target:
            correct += 1
        print(f"输入: {inputs}, 预测类别: {predicted_class}, 目标: {target}, "
              f"{'✓' if predicted_class == target else '✗'}")
    
    accuracy = correct / len(training_data) * 100
    print(f"\n总体准确率: {accuracy:.1f}%")


if __name__ == "__main__":
    main()
