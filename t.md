AI编程工具介绍
介绍
本文主要介绍AI编程工具的工作模式，几种常见的使用场景，和使用过程中遇到的一些问题。

AI编程的几种模式
AI编程工具大部分支持tab、chat、agent几种模式。
tab模式
在代码编辑器中实时补全代码。类似智能提示，但更智能。有时我想自己改改代码，刚写几个单词，AI就会预测出数行代码，并且都是我想要的。按tab接受后，AI会继续提示下一处修改。
https://drive.weixin.qq.com/s?k=ADkAmAf9AA4Acv0l3uATsACgYMAP0 

chat 模式
通过对话窗口与AI交互，解答问题或生成代码片段。


agent模式
AI作为自主代理，分析项目需求并生成完整代码模块。
用户提出需求后，agent可以分析、规划和执行任务。
分析： agent可以结合上下文(先前的对话、当前项目代码、选中的文档和代码、配置的规则)识别用户的意图，避免出现歧义。
规划：对于复杂需求，AI会拆解任务，规划步骤。
执行：有了规划后，AI按步骤调用工具执行任务。这里的工具既包括基本的文档读写也包括操作系统自带工具。

在日常工作中，我主要使用agent模式，将大部分工作交给AI自主执行。我把主要精力放在前期设计和后期质量保证。

使用场景
新项目、新模块开发
AI编程和手写编程对比
没有AI编程工具前，开发需要编写详细的技术设计文档，一行一行手写代码(虽然IDE代码提示可以让我们少打字)。
现在有了AI编程工具，我会这么来开发项目:
1.简单编写需求文档，列出功能点、技术栈、代码目录结构等
2.打开编程工具的agent(自动)模式，把文档丢给它，告诉它按文档实现项目，让AI生成几乎所有代码
3.在AI生成代码基础上继续和AI agent对话，调整代码，修复问题

从需求文档到代码，agent使用示例
1.编写需求文档


2.交给AI agent执行


3.收获代码


4.代码量统计
花了1个小时， AI写的



总结
AI agent可以是一个任劳任怨的工程师；程序员的角色转变成产品经理+技术架构师

新技术学习
可以利用AI agent来加速新技术学习。
比如我想了解神经网络的基本技术原理
1.先让AI agent写一份代码


    生成的代码


2.有疑问随时提问
打破砂锅问到底也没关系，AI会耐心解释。




总结 
AI agent也是我们学习的良师益友，它们内置了各种知识宝库，我们可以通过原型代码和实战项目快速学习。
想在项目中采用新技术、想了解技术原理和内部实现、想了解新的技术方向，都可以通过AI编程工具做到。

新项目学习
当我们拿到一份新项目代码时，可以先让AI整体分析，给出一份报告。
比如我问AI: kirara-ai这个项目的架构设计怎么样

另外，还可以借助deepwiki工具来熟悉github开源项目。
也可以分析多个项目来做深度技术选型。

问题排查解决
除了开发新项目， AI agent在解决已有系统bug时，也能提高速度。
1.大部分时候，我们把报错信息贴给AI，它就能自己修复问题。
2.如果没有错误信息，比如单纯的逻辑错误。我们可以把现象用文字描述给AI，让它解决
3.如果问题比较复杂， AI没法自行解决。我们可以指导AI增加调试输出，打印调用栈，方便调试。


当前AI编程工具的不足
1.功能点遗漏
首先确认AI编程工具使用的模型，顶级的模型(比如claude4)一般不会遗漏需求。
除非需求过于复杂，AI理解不了，或者一次请求无法完成。 需要我们细化和拆分需求。

2.多生成功能和代码
这个问题很常见。
有时我们觉得AI善解人意，虽然需求里没写，它也拓展帮我们完成，并且正是我们想要的。
但有时在我们看来是画蛇添足。处理办法有：
	a. 事前： 在需求文档或者给AI的规则里，要求它只实现我们要的功能
	b. 事中:    AI输出代码时，发现生成了不想要的功能，马上纠正它
	c. 事后:   检查AI生成的代码，手动或者告诉AI删除不需要的代码

3. 代码质量低
	AI有时会生成低质量代码。 比如同样功能的代码在一个项目里实现很多次。这种需要我们在给AI指令时就明确要求，或者事后重构。
	AI写代码能力很强，但在架构方面比较弱，目前还需要我们给与指导。


AI上手的一些问题

问题和解决方法
1.区域限制，无法使用一流模型
claude 4等一流编程模型在国内默认无法使用。目前只能fq付费使用。
2.前后端代码不同仓库，AI生成的代码前后端接口没对齐
前后端代码放同工程下不同目录，AI可参考。另外也可以用规则来说明接口请求响应格式。

了解过的AI编程工具
工具	说明
cursor	4月份刚接触AI编程时开始用， 免费试用claude3.7效果不错，用它完成了2个中型项目。
近期需要绑卡才能试用，且claude高级模型中国大陆不让用。套餐相对便宜。
augment	cursor使用困难后转到augment，可以免费试用claude4.0，有明显提升。近期试用越来越困难，套餐比较贵。
cluade code	纯命令行，据说很强。还没体验过。
kiro	亚马逊新出，测试期不稳定。
copilot	微软家的，很早就开始做AI编程，但对agent模式支持比较晚。
trae	字节的，网上有评价说效果比不上cursor、augment。
roo code/cline	开源的，需要自己绑模型
 目前AI agent编程比较依赖模型，推荐能用claude4模型的工具。


